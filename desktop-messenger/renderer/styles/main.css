/* CSS Variables for Theme Management */
:root {
  /* Primary Colors */
  --primary-50: #ecfdf5;
  --primary-100: #d1fae5;
  --primary-200: #a7f3d0;
  --primary-300: #6ee7b7;
  --primary-400: #34d399;
  --primary-500: #10b981;
  --primary-600: #059669;
  --primary-700: #047857;
  --primary-800: #065f46;
  --primary-900: #064e3b;

  /* Secondary Colors */
  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;

  /* Accent Colors */
  --accent-purple: #8b5cf6;
  --accent-blue: #3b82f6;
  --accent-pink: #ec4899;
  --accent-orange: #f59e0b;

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Semantic Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Typography */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* Light Theme Variables */
.theme-light {
  --background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  --background-solid: #f8fafc;
  --background-secondary: #ffffff;
  --background-tertiary: #f1f5f9;
  --foreground: #1e293b;
  --foreground-muted: #64748b;
  --foreground-secondary: #475569;
  --border-color: #e2e8f0;
  --border-color-light: #f1f5f9;
  --card-background: rgba(255, 255, 255, 0.8);
  --glass-background: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
}

/* Dark Theme Variables */
.theme-dark {
  --background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  --background-solid: #0f172a;
  --background-secondary: #1e293b;
  --background-tertiary: #334155;
  --foreground: #f8fafc;
  --foreground-muted: #94a3b8;
  --foreground-secondary: #cbd5e1;
  --border-color: #334155;
  --border-color-light: #475569;
  --card-background: rgba(30, 41, 59, 0.8);
  --glass-background: rgba(15, 23, 42, 0.25);
  --glass-border: rgba(148, 163, 184, 0.18);
}

/* Dark theme specific overrides */
.theme-dark .btn-secondary {
  background: rgba(30, 41, 59, 0.9);
  border-color: var(--border-color);
  color: var(--foreground);
}

.theme-dark .btn-secondary:hover {
  background: rgba(51, 65, 85, 0.9);
  border-color: var(--primary-400);
}

.theme-dark .message.other {
  background-color: var(--background-tertiary);
  color: var(--foreground);
}

.theme-dark .message-input {
  background: var(--background-secondary);
  border-color: var(--border-color);
  color: var(--foreground);
}

.theme-dark .message-input:focus {
  border-color: var(--primary-400);
}

.theme-dark .contact-preview {
  color: var(--foreground-muted);
}

.theme-dark .offline-indicator {
  background-color: var(--warning);
  color: var(--white);
}

/* Utility Classes */
.glass-effect {
  background: var(--glass-background);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
}

.glass-card {
  background: var(--card-background);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: var(--card-background);
  box-shadow: var(--shadow-2xl);
  transform: translateY(-4px);
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary-600), var(--accent-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card {
  background: var(--background-secondary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-500), var(--accent-purple), var(--accent-blue));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card:hover::before {
  opacity: 1;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-sans);
    background: var(--background);
    color: var(--foreground);
    overflow: hidden;
    transition: background 0.3s ease, color 0.3s ease;
}

#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Loading screen */
.loading-screen {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: var(--background);
    color: var(--foreground);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-300);
    border-top: 4px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Screen management */
.screen {
    height: 100vh;
    width: 100vw;
}

/* Login screen */
.login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background:
        radial-gradient(circle at 20% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
        var(--background);
    color: var(--foreground);
    position: relative;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 1px 1px, rgba(16, 185, 129, 0.1) 1px, transparent 0);
    background-size: 20px 20px;
    opacity: 0.3;
    pointer-events: none;
}

.login-header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    z-index: 1;
}

.login-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    background: linear-gradient(135deg, var(--primary-600), var(--accent-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-form {
    background: var(--card-background);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    padding: 40px;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    min-width: 400px;
    position: relative;
    z-index: 1;
}

.form-group {
    margin-bottom: var(--space-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--space-sm);
    font-weight: 600;
    color: var(--foreground);
    font-size: 0.875rem;
    letter-spacing: 0.025em;
}

.form-group input {
    width: 100%;
    padding: var(--space-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--background-secondary);
    color: var(--foreground);
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.form-group input::placeholder {
    color: var(--foreground-muted);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-500);
    background: var(--background-secondary);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1), var(--shadow-md);
    transform: translateY(-1px);
}

.login-footer {
    text-align: center;
    margin-top: var(--space-lg);
    position: relative;
    z-index: 1;
}

.login-footer a {
    color: var(--primary-500);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border-bottom: 1px solid transparent;
}

.login-footer a:hover {
    color: var(--primary-600);
    border-bottom-color: var(--primary-500);
    transform: translateY(-1px);
}

/* Chat screen */
#chat-screen {
    display: flex;
    height: 100vh;
    background: var(--background);
}

/* Sidebar */
.sidebar {
    width: 320px;
    background: var(--card-background);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-right: 1px solid var(--border-color);
    color: var(--foreground);
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-lg);
}

.sidebar-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--background-tertiary);
}

.user-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: var(--space-md);
    object-fit: cover;
    border: 2px solid var(--primary-500);
    box-shadow: var(--shadow-md);
}

.user-details h3 {
    font-size: 16px;
    margin-bottom: 2px;
    color: var(--foreground);
    font-weight: 600;
}

.user-status {
    font-size: 12px;
    color: var(--foreground-muted);
}

.sidebar-actions {
    display: flex;
    gap: var(--space-sm);
}

.search-container {
    padding: var(--space-md) var(--space-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--background-secondary);
}

.search-input {
    width: 100%;
    padding: var(--space-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    background: var(--background-secondary);
    color: var(--foreground);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    font-size: 14px;
}

.search-input::placeholder {
    color: var(--foreground-muted);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1), var(--shadow-md);
    transform: translateY(-1px);
}

.contacts-list {
    flex: 1;
    overflow-y: auto;
    background: var(--background-secondary);
}

.contact-item {
    display: flex;
    align-items: center;
    padding: var(--space-md) var(--space-lg);
    cursor: pointer;
    border-bottom: 1px solid var(--border-color-light);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.contact-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-500), var(--accent-purple), var(--accent-blue));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.contact-item:hover {
    background: var(--background-tertiary);
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.contact-item:hover::before {
    opacity: 1;
}

.contact-item.active {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: var(--white);
    box-shadow: var(--shadow-lg);
}

.contact-item.active::before {
    opacity: 1;
}

.contact-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    margin-right: var(--space-md);
    object-fit: cover;
    position: relative;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.contact-item:hover .contact-avatar {
    border-color: var(--primary-500);
    transform: scale(1.05);
}

.contact-item.active .contact-avatar {
    border-color: var(--white);
}

.contact-info {
    flex: 1;
}

.contact-name {
    font-size: 16px;
    margin-bottom: 2px;
    font-weight: 600;
    color: var(--foreground);
}

.contact-item.active .contact-name {
    color: var(--white);
}

.contact-preview {
    font-size: 13px;
    color: var(--foreground-muted);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.online-indicator {
    width: 12px;
    height: 12px;
    background-color: #2ecc71;
    border-radius: 50%;
    position: absolute;
    bottom: 0;
    right: 0;
    border: 2px solid #2c3e50;
}

/* Main chat area */
.main-chat {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

.no-chat-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #95a5a6;
    text-align: center;
}

.chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-header {
    padding: 20px;
    border-bottom: 1px solid #ecf0f1;
    background-color: #fff;
}

.contact-info {
    display: flex;
    align-items: center;
}

.contact-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
}

.contact-details h3 {
    font-size: 18px;
    margin-bottom: 2px;
    color: #2c3e50;
}

.contact-status {
    font-size: 14px;
    color: #95a5a6;
}

/* Messages */
.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #f8f9fa;
}

.messages-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.message {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    position: relative;
}

.message.own {
    align-self: flex-end;
    background-color: #007bff;
    color: white;
    border-bottom-right-radius: 4px;
}

.message.other {
    align-self: flex-start;
    background-color: #e9ecef;
    color: #333;
    border-bottom-left-radius: 4px;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 4px;
}

.message.sending {
    opacity: 0.6;
}

.message.sending::after {
    content: " (sending...)";
    font-size: 11px;
    opacity: 0.7;
}

/* Message input */
.message-input-container {
    border-top: 1px solid #ecf0f1;
    background-color: #fff;
    position: relative; /* Added for emoji picker positioning */
}

.offline-indicator {
    background-color: #f39c12;
    color: white;
    padding: 10px 20px;
    text-align: center;
    font-size: 14px;
}

.message-form {
    padding: 20px;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.message-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 25px;
    font-size: 14px;
    outline: none;
}

.message-input:focus {
    border-color: #007bff;
}

/* Buttons */
.btn {
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    border: none;
    flex-shrink: 0;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    font-family: var(--font-family-sans);
    letter-spacing: 0.025em;
    box-shadow: var(--shadow-sm);
    padding: var(--space-sm) var(--space-lg);
    font-size: 14px;
    text-decoration: none;
    text-align: center;
}

.btn::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn:hover::before {
    opacity: 1;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.btn-primary:disabled {
    background: var(--gray-400);
    cursor: not-allowed;
    transform: none;
    box-shadow: var(--shadow-sm);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: var(--foreground);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 1);
    border-color: var(--primary-300);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.btn-icon {
    padding: var(--space-sm);
    border-radius: 50%;
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    cursor: pointer;
    pointer-events: auto;
    position: relative;
    z-index: 10;
    min-width: 40px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.btn-icon:hover {
    background: var(--background-tertiary);
    border-color: var(--primary-500);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: #007bff;
}

.btn-icon:active {
    background-color: #e9ecef;
    transform: scale(0.95);
}

/* Emoji picker */
.emoji-picker {
    position: absolute;
    bottom: 100%;
    left: 20px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 5px;
    width: 300px;
    max-width: calc(100vw - 60px); /* Ensure it doesn't exceed viewport width */
    max-height: 200px;
    overflow: hidden; /* Prevent content from flowing outside */
    overflow-y: auto; /* Allow vertical scrolling if needed */
    z-index: 1000;
    box-sizing: border-box; /* Include padding and border in width calculation */
}

.emoji-btn {
    padding: 8px;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: 4px;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 0; /* Allow button to shrink */
    overflow: hidden; /* Prevent emoji from overflowing button */
    text-overflow: ellipsis;
    white-space: nowrap;
    box-sizing: border-box;
}

.emoji-btn:hover {
    background-color: #f8f9fa;
}

/* Responsive emoji picker for smaller screens */
@media (max-width: 480px) {
    .emoji-picker {
        left: 10px;
        right: 10px;
        width: auto;
        max-width: calc(100vw - 40px);
        grid-template-columns: repeat(6, 1fr); /* Fewer columns on small screens */
    }

    .emoji-btn {
        font-size: 16px;
        padding: 6px;
    }
}

/* Error messages */
.error-message {
    background-color: #dc3545;
    color: white;
    padding: 10px;
    border-radius: 5px;
    margin-top: 15px;
    text-align: center;
}

/* Offline notification */
.offline-notification {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: #f39c12;
    color: white;
    padding: 10px;
    text-align: center;
    z-index: 1000;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
